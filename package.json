{"name": "u-package", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": ">=20.0.0", "pnpm": ">=8.0.0"}, "scripts": {"preinstall": "npx only-allow pnpm", "dev": "vite", "build:web": "cd packages/web && pnpm run build", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.5.17"}, "devDependencies": {"@iconify/utils": "^2.3.0", "@playwright/test": "^1.53.1", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/node": "^22.15.32", "@unocss/preset-icons": "^66.3.3", "@unocss/transformer-directives": "^66.3.3", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.0", "@vitest/eslint-plugin": "^1.2.7", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "@vueuse/core": "^13.5.0", "@xiaou66/u-web-ui": "workspace:*", "es-toolkit": "^1.39.7", "eslint": "^9.29.0", "eslint-plugin-oxlint": "~1.1.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "less": "^4.4.0", "npm-run-all2": "^8.0.4", "only-allow": "^1.2.1", "oxlint": "~1.1.0", "prettier": "3.5.3", "tdesign-vue-next": "^1.15.1", "tslib": "^2.8.1", "typescript": "~5.8.0", "unocss": "^66.3.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "npm:rolldown-vite@latest", "vite-plugin-dts": "^4.5.4", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4", "vue-component-type-helpers": "^3.0.4", "vue-router": "^4.5.1", "vue-tsc": "^2.2.10"}}