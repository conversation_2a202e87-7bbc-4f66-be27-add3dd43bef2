

/**
 * 触发方式
 */
export type TriggerType = 'hover' | 'click' | 'focus' | 'contextmenu'

/**
 * 弹出位置
 */
export type TriggerPlacement =
  | 'top' | 'top-start' | 'top-end'
  | 'bottom' | 'bottom-start' | 'bottom-end'
  | 'left' | 'left-start' | 'left-end'
  | 'right' | 'right-start' | 'right-end'

/**
 * Trigger 组件的 Props 类型
 */
export interface TriggerProps {
  /**
   * 是否显示弹出内容
   */
  visible?: boolean
  /**
   * 触发方式
   * @default 'hover'
   */
  trigger?: TriggerType | TriggerType[]
  /**
   * 弹出位置
   * @default 'bottom'
   */
  placement?: TriggerPlacement
  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean
  /**
   * 延迟显示时间（毫秒）
   * @default 100
   */
  showDelay?: number
  /**
   * 延迟隐藏时间（毫秒）
   * @default 100
   */
  hideDelay?: number
  /**
   * 弹出内容与触发元素的距离
   * @default 8
   */
  offset?: number
  /**
   * 是否显示箭头
   * @default true
   */
  showArrow?: boolean
  /**
   * 弹出层的 z-index
   * @default 1000
   */
  zIndex?: number
  /**
   * 弹出层的类名
   */
  popupClass?: string
  /**
   * 弹出层的样式
   */
  popupStyle?: Record<string, any>
  /**
   * 是否在触发元素不可见时隐藏弹出层
   * @default true
   */
  hideOnInvisible?: boolean
  /**
   * 是否在点击外部时隐藏弹出层
   * @default true
   */
  hideOnClickOutside?: boolean
}

/**
 * Trigger 组件的 Emits 类型
 */
export interface TriggerEmits {
  /**
   * 显示状态变化时触发
   * @param visible 是否显示
   */
  'update:visible': [visible: boolean]
  /**
   * 显示前触发
   */
  'before-show': []
  /**
   * 显示后触发
   */
  'show': []
  /**
   * 隐藏前触发
   */
  'before-hide': []
  /**
   * 隐藏后触发
   */
  'hide': []
}
