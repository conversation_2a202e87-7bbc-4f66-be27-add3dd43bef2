// Trigger 组件全局样式
.u-trigger {
  display: inline-block;

  &__reference {
    display: inline-block;
    cursor: pointer;
  }

  &__popup {
    position: fixed;
    background: var(--u-bg-color-3, #fff);
    border: 1px solid var(--u-color-neutral-3, #e0e0e0);
    border-radius: var(--u-radius-default, 6px);
    box-shadow: var(--u-shadow-lg);
    padding: 8px 12px;
    font-size: 14px;
    line-height: 1.5;
    color: var(--u-text-color-primary, #333);
    max-width: 300px;
    word-wrap: break-word;
    z-index: 1000;

    &--top {
      transform-origin: center bottom;
    }

    &--bottom {
      transform-origin: center top;
    }

    &--left {
      transform-origin: right center;
    }

    &--right {
      transform-origin: left center;
    }
  }

  &__arrow {
    position: absolute;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    pointer-events: none;
  }

  &__content {
    position: relative;
    z-index: 1;
  }
}

// 过渡动画
.u-trigger-fade-enter-active,
.u-trigger-fade-leave-active {
  transition: all 0.2s ease;
}

.u-trigger-fade-enter-from,
.u-trigger-fade-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

.u-trigger-fade-enter-to,
.u-trigger-fade-leave-from {
  opacity: 1;
  transform: scale(1);
}
