<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import type { TriggerProps, TriggerEmits } from './interface'

// 定义组件选项
defineOptions({
  name: 'Trigger',
  inheritAttrs: false,
})

// 定义 props
const props = withDefaults(defineProps<TriggerProps>(), {
  trigger: 'hover',
  placement: 'bottom',
  disabled: false,
  showDelay: 150,
  hideDelay: 150,
  offset: 8,
  showArrow: true,
  zIndex: 1000,
  hideOnInvisible: true,
  hideOnClickOutside: true,
})

// 定义 emits
const emit = defineEmits<TriggerEmits>()

// 响应式数据
const triggerRef = ref<HTMLElement>()
const popupRef = ref<HTMLElement>()
const arrowRef = ref<HTMLElement>()
const isVisible = ref(false)
const showTimer = ref<number>()
const hideTimer = ref<number>()

// 计算属性
const triggerTypes = computed(() => {
  return Array.isArray(props.trigger) ? props.trigger : [props.trigger]
})

const popupStyle = computed(() => {
  return {
    zIndex: props.zIndex,
    ...props.popupStyle,
  }
})

// 显示弹出层
const show = () => {
  if (props.disabled || isVisible.value) return

  clearTimeout(hideTimer.value)

  if (props.showDelay > 0) {
    showTimer.value = window.setTimeout(() => {
      doShow()
    }, props.showDelay)
  } else {
    doShow()
  }
}

// 隐藏弹出层
const hide = () => {
  if (!isVisible.value) return

  clearTimeout(showTimer.value)

  if (props.hideDelay > 0) {
    hideTimer.value = window.setTimeout(() => {
      doHide()
    }, props.hideDelay)
  } else {
    doHide()
  }
}

// 执行显示
const doShow = () => {
  emit('before-show')
  isVisible.value = true
  emit('update:visible', true)

  nextTick(() => {
    updatePosition()
    emit('show')
  })
}

// 执行隐藏
const doHide = () => {
  emit('before-hide')
  isVisible.value = false
  emit('update:visible', false)
  emit('hide')
}

// 更新位置
const updatePosition = () => {
  if (!triggerRef.value || !popupRef.value) return

  const triggerRect = triggerRef.value.getBoundingClientRect()
  const popupRect = popupRef.value.getBoundingClientRect()
  const { placement, offset } = props

  let top = 0
  let left = 0

  // 根据 placement 计算位置
  switch (placement) {
    case 'top':
      top = triggerRect.top - popupRect.height - offset
      left = triggerRect.left + (triggerRect.width - popupRect.width) / 2
      break
    case 'top-start':
      top = triggerRect.top - popupRect.height - offset
      left = triggerRect.left
      break
    case 'top-end':
      top = triggerRect.top - popupRect.height - offset
      left = triggerRect.right - popupRect.width
      break
    case 'bottom':
      top = triggerRect.bottom + offset
      left = triggerRect.left + (triggerRect.width - popupRect.width) / 2
      break
    case 'bottom-start':
      top = triggerRect.bottom + offset
      left = triggerRect.left
      break
    case 'bottom-end':
      top = triggerRect.bottom + offset
      left = triggerRect.right - popupRect.width
      break
    case 'left':
      top = triggerRect.top + (triggerRect.height - popupRect.height) / 2
      left = triggerRect.left - popupRect.width - offset
      break
    case 'left-start':
      top = triggerRect.top
      left = triggerRect.left - popupRect.width - offset
      break
    case 'left-end':
      top = triggerRect.bottom - popupRect.height
      left = triggerRect.left - popupRect.width - offset
      break
    case 'right':
      top = triggerRect.top + (triggerRect.height - popupRect.height) / 2
      left = triggerRect.right + offset
      break
    case 'right-start':
      top = triggerRect.top
      left = triggerRect.right + offset
      break
    case 'right-end':
      top = triggerRect.bottom - popupRect.height
      left = triggerRect.right + offset
      break
  }

  // 确保弹出层不会覆盖触发元素（防止抖动）
  const minGap = 2 // 最小间隙
  if (placement.startsWith('top') && top + popupRect.height + minGap > triggerRect.top) {
    top = triggerRect.top - popupRect.height - minGap
  } else if (placement.startsWith('bottom') && top - minGap < triggerRect.bottom) {
    top = triggerRect.bottom + minGap
  } else if (placement.startsWith('left') && left + popupRect.width + minGap > triggerRect.left) {
    left = triggerRect.left - popupRect.width - minGap
  } else if (placement.startsWith('right') && left - minGap < triggerRect.right) {
    left = triggerRect.right + minGap
  }

  // 边界检测和调整
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  if (left < 0) left = 0
  if (left + popupRect.width > viewportWidth) {
    left = viewportWidth - popupRect.width
  }
  if (top < 0) top = 0
  if (top + popupRect.height > viewportHeight) {
    top = viewportHeight - popupRect.height
  }

  popupRef.value.style.top = `${top}px`
  popupRef.value.style.left = `${left}px`

  // 更新箭头位置
  if (props.showArrow && arrowRef.value) {
    updateArrowPosition(triggerRect, { top, left, width: popupRect.width, height: popupRect.height })
  }
}

// 更新箭头位置
const updateArrowPosition = (triggerRect: DOMRect, popupRect: { top: number; left: number; width: number; height: number }) => {
  if (!arrowRef.value) return

  const { placement } = props
  const arrowSize = 6 // 箭头大小

  if (placement.startsWith('top')) {
    arrowRef.value.style.top = '100%'
    arrowRef.value.style.left = `${triggerRect.left + triggerRect.width / 2 - popupRect.left - arrowSize}px`
    arrowRef.value.style.borderTopColor = 'var(--u-bg-color-3, #fff)'
    arrowRef.value.style.borderBottomColor = 'transparent'
  } else if (placement.startsWith('bottom')) {
    arrowRef.value.style.top = `-${arrowSize}px`
    arrowRef.value.style.left = `${triggerRect.left + triggerRect.width / 2 - popupRect.left - arrowSize}px`
    arrowRef.value.style.borderBottomColor = 'var(--u-bg-color-3, #fff)'
    arrowRef.value.style.borderTopColor = 'transparent'
  } else if (placement.startsWith('left')) {
    arrowRef.value.style.left = '100%'
    arrowRef.value.style.top = `${triggerRect.top + triggerRect.height / 2 - popupRect.top - arrowSize}px`
    arrowRef.value.style.borderLeftColor = 'var(--u-bg-color-3, #fff)'
    arrowRef.value.style.borderRightColor = 'transparent'
  } else if (placement.startsWith('right')) {
    arrowRef.value.style.left = `-${arrowSize}px`
    arrowRef.value.style.top = `${triggerRect.top + triggerRect.height / 2 - popupRect.top - arrowSize}px`
    arrowRef.value.style.borderRightColor = 'var(--u-bg-color-3, #fff)'
    arrowRef.value.style.borderLeftColor = 'transparent'
  }
}

// 事件处理
const handleMouseEnter = () => {
  if (triggerTypes.value.includes('hover')) {
    clearTimeout(hideTimer.value)
    show()
  }
}

const handleMouseLeave = () => {
  if (triggerTypes.value.includes('hover')) {
    hide()
  }
}

// 弹出层的鼠标事件处理
const handlePopupMouseEnter = () => {
  if (triggerTypes.value.includes('hover')) {
    clearTimeout(hideTimer.value)
  }
}

const handlePopupMouseLeave = () => {
  if (triggerTypes.value.includes('hover')) {
    hide()
  }
}

const handleClick = () => {
  if (triggerTypes.value.includes('click')) {
    if (isVisible.value) {
      hide()
    } else {
      show()
    }
  }
}

const handleFocus = () => {
  if (triggerTypes.value.includes('focus')) {
    show()
  }
}

const handleBlur = () => {
  if (triggerTypes.value.includes('focus')) {
    hide()
  }
}

const handleContextMenu = (e: MouseEvent) => {
  if (triggerTypes.value.includes('contextmenu')) {
    e.preventDefault()
    show()
  }
}

// 点击外部隐藏
const handleClickOutside = (e: MouseEvent) => {
  if (!props.hideOnClickOutside || !isVisible.value) return

  const target = e.target as Node
  if (
    triggerRef.value?.contains(target) ||
    popupRef.value?.contains(target)
  ) {
    return
  }

  hide()
}

// 监听 visible prop 变化
watch(() => props.visible, (newVisible) => {
  if (newVisible !== undefined) {
    if (newVisible && !isVisible.value) {
      show()
    } else if (!newVisible && isVisible.value) {
      hide()
    }
  }
})

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  window.addEventListener('resize', updatePosition)
  window.addEventListener('scroll', updatePosition)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('resize', updatePosition)
  window.removeEventListener('scroll', updatePosition)
  clearTimeout(showTimer.value)
  clearTimeout(hideTimer.value)
})
</script>

<template>
  <div class="u-trigger">
    <!-- 触发元素 -->
    <div
      ref="triggerRef"
      class="u-trigger__reference"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @click="handleClick"
      @focus="handleFocus"
      @blur="handleBlur"
      @contextmenu="handleContextMenu"
    >
      <slot />
    </div>

    <!-- 弹出层 -->
    <Teleport to="body">
      <Transition
        name="u-trigger-fade"
        @enter="updatePosition"
        @after-enter="updatePosition"
      >
        <div
          v-show="isVisible"
          ref="popupRef"
          class="u-trigger__popup"
          :class="[
            `u-trigger__popup--${placement}`,
            props.popupClass
          ]"
          :style="popupStyle"
          @mouseenter="handlePopupMouseEnter"
          @mouseleave="handlePopupMouseLeave"
        >
          <!-- 箭头 -->
          <div
            v-if="showArrow"
            ref="arrowRef"
            class="u-trigger__arrow"
          />

          <!-- 弹出内容 -->
          <div class="u-trigger__content">
            <slot name="content" />
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<style scoped lang="less">
.u-trigger {
  display: inline-block;

  &__reference {
    display: inline-block;
    cursor: pointer;
  }

  &__popup {
    position: fixed;
    background: var(--u-bg-color-3, #fff);
    border: 1px solid var(--u-color-neutral-3, #e0e0e0);
    border-radius: var(--u-radius-default, 6px);
    box-shadow: var(--u-shadow-lg);
    padding: 8px 12px;
    font-size: 14px;
    line-height: 1.5;
    color: var(--u-text-color-primary, #333);
    max-width: 300px;
    word-wrap: break-word;
    z-index: 1000;

    &--top {
      transform-origin: center bottom;
    }

    &--bottom {
      transform-origin: center top;
    }

    &--left {
      transform-origin: right center;
    }

    &--right {
      transform-origin: left center;
    }
  }

  &__arrow {
    position: absolute;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    pointer-events: none;
  }

  &__content {
    position: relative;
    z-index: 1;
  }
}

// 过渡动画
.u-trigger-fade-enter-active,
.u-trigger-fade-leave-active {
  transition: all 0.2s ease;
}

.u-trigger-fade-enter-from,
.u-trigger-fade-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

.u-trigger-fade-enter-to,
.u-trigger-fade-leave-from {
  opacity: 1;
  transform: scale(1);
}
</style>
