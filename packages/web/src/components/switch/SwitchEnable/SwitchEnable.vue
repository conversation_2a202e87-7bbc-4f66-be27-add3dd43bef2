<script lang="tsx" setup>
import type { SwitchProps } from 'tdesign-vue-next'
import type { SwitchEnableProps, SwitchEnableEmits } from './interface'

// 定义组件选项
defineOptions({
  name: 'SwitchEnable',
  inheritAttrs: false,
})

// 定义 props
const props = withDefaults(defineProps<SwitchEnableProps>(), {
  color: 'green',
  size: 'large',
});

// 定义 emits，支持 change 事件
const emit = defineEmits<SwitchEnableEmits>()

const value = defineModel<boolean>('value', {
  default: false,
});

const handleChange = (newValue: boolean, context: { e: Event }) => {
  emit('change', newValue, context)
}
const renderContent: SwitchProps['label'] = (h, data) => {
  return data.value
    ? <span class="i-p-check" style="width: 10px; height: 10px;" />
    : <span class="i-p-close" style="width: 10px; height: 10px;" />;
};
</script>

<template>
  <t-switch
    class="u-web-switch"
    :class="{ 'u-web-switch-green': props.color === 'green' }"
    v-bind="{ ...props, value: undefined }"
    v-model="value"
    @change="handleChange"
    :label="renderContent"
  >
  </t-switch>
</template>

<style lang="less">
.u-web-switch-green {
  transition: background-color 0.2s linear;
  &.t-is-checked {
    background-color: rgba(var(--green-5));
  }
  &.t-is-checked:hover {
    background-color: rgba(var(--green-6));
  }
}
</style>
