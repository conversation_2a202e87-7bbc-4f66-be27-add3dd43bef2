<script setup lang="ts">
import { ref } from 'vue';
import { SplitPanel } from '@xiaou66/u-web-ui';

const size = ref<number | string>(0.3);
const verticalSize = ref<number | string>('200px');

function onSizeChange(newSize: number | string) {
  console.log('尺寸改变:', newSize);
}

function onMoveStart(e: MouseEvent) {
  console.log('开始拖拽:', e);
}

function onMoving(e: MouseEvent) {
  console.log('拖拽中:', e);
}

function onMoveEnd(e: MouseEvent) {
  console.log('拖拽结束:', e);
}
</script>

<template>
  <div class="demo-container">
    <h1>SplitPanel 分割面板演示</h1>

    <div class="demo-section">
      <h2>水平分割</h2>
      <div class="demo-box">
        <SplitPanel
          v-model:size="size"
          :min="0.2"
          :max="0.8"
          @update:size="onSizeChange"
          @move-start="onMoveStart"
          @moving="onMoving"
          @move-end="onMoveEnd"
        >
          <template #first>
            <div class="panel-content panel-left">
              <h3>左侧面板</h3>
              <p>当前尺寸: {{ size }}</p>
              <p>这是第一个面板的内容，可以自由调整大小。</p>
              <p>最小值: 20%, 最大值: 80%</p>
            </div>
          </template>
          <template #second>
            <div class="panel-content panel-right">
              <h3>右侧面板</h3>
              <p>这是第二个面板的内容。</p>
              <p>会自动占据剩余空间。</p>
              <ul>
                <li>支持响应式调整</li>
                <li>支持最小/最大限制</li>
                <li>流畅的拖拽体验</li>
              </ul>
            </div>
          </template>
        </SplitPanel>
      </div>
    </div>

    <div class="demo-section">
      <h2>垂直分割</h2>
      <div class="demo-box">
        <SplitPanel
          v-model:size="verticalSize"
          direction="vertical"
          :min="100"
          :max="300"
        >
          <template #first>
            <div class="panel-content panel-top">
              <h3>上方面板</h3>
              <p>当前尺寸: {{ verticalSize }}</p>
              <p>垂直分割模式，固定像素值控制。</p>
            </div>
          </template>
          <template #second>
            <div class="panel-content panel-bottom">
              <h3>下方面板</h3>
              <p>自动占据剩余空间的下方面板。</p>
              <p>最小高度: 100px, 最大高度: 300px</p>
            </div>
          </template>
        </SplitPanel>
      </div>
    </div>

    <div class="demo-section">
      <h2>自定义拖拽图标</h2>
      <div class="demo-box">
        <SplitPanel :default-size="0.4">
          <template #first>
            <div class="panel-content panel-left">
              <h3>左侧面板</h3>
              <p>带有自定义拖拽图标的分割面板。</p>
            </div>
          </template>
          <template #second>
            <div class="panel-content panel-right">
              <h3>右侧面板</h3>
              <p>右侧面板内容。</p>
            </div>
          </template>
          <template #resize-trigger-icon>
            <div class="custom-trigger">
              <span>⋮⋮</span>
            </div>
          </template>
        </SplitPanel>
      </div>
    </div>

    <div class="demo-section">
      <h2>禁用状态</h2>
      <div class="demo-box">
        <SplitPanel :default-size="0.5" disabled>
          <template #first>
            <div class="panel-content panel-left">
              <h3>左侧面板</h3>
              <p>这个分割面板被禁用了，无法拖拽调整。</p>
            </div>
          </template>
          <template #second>
            <div class="panel-content panel-right">
              <h3>右侧面板</h3>
              <p>禁用状态下的右侧面板。</p>
            </div>
          </template>
        </SplitPanel>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;

  h2 {
    margin-bottom: 16px;
    color: var(--u-text-color-primary, #333);
    border-bottom: 2px solid var(--u-color-neutral-3, #e0e0e0);
    padding-bottom: 8px;
  }
}

.demo-box {
  height: 300px;
  border: 1px solid var(--u-color-neutral-3, #e0e0e0);
  border-radius: var(--u-radius-medium, 8px);
  overflow: hidden;
  box-shadow: var(--u-shadow-sm, 0 1px 2px rgba(0,0,0,0.1));
}

.panel-content {
  height: 100%;
  padding: 20px;
  overflow-y: auto;

  h3 {
    margin-top: 0;
    margin-bottom: 16px;
    color: var(--u-text-color-primary, #333);
  }

  p {
    margin-bottom: 12px;
    color: var(--u-text-color-secondary, #666);
    line-height: 1.5;
  }

  ul {
    padding-left: 20px;

    li {
      margin-bottom: 8px;
      color: var(--u-text-color-secondary, #666);
    }
  }
}

.panel-left {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  h3, p, li {
    color: white;
  }
}

.panel-right {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;

  h3, p, li {
    color: white;
  }
}

.panel-top {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;

  h3, p {
    color: white;
  }
}

.panel-bottom {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;

  h3, p {
    color: white;
  }
}

.custom-trigger {
  font-size: 16px;
  font-weight: bold;
  color: var(--u-text-color-brand, #1890ff);
}
</style>
