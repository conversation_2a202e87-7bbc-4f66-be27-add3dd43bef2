<script setup lang="ts">
import { ref } from 'vue'
import { Trigger } from '@xiaou66/u-web-ui'

const visible1 = ref(false)
const visible2 = ref(false)
const visible3 = ref(false)

const handleVisibleChange = (visible: boolean, name: string) => {
  console.log(`${name} 显示状态变化:`, visible)
}

const handleShow = (name: string) => {
  console.log(`${name} 显示`)
}

const handleHide = (name: string) => {
  console.log(`${name} 隐藏`)
}
</script>

<template>
  <div class="demo-container">
    <h1>Trigger 触发器组件演示</h1>

    <div class="demo-section">
      <h2>基础用法</h2>
      <div class="demo-row">
        <Trigger
          trigger="hover"
          placement="top"
          @update:visible="(v) => handleVisibleChange(v, 'hover')"
          @show="() => handleShow('hover')"
          @hide="() => handleHide('hover')"
        >
          <t-button>悬停触发</t-button>
          <template #content>
            <div>这是一个悬停触发的提示内容</div>
          </template>
        </Trigger>

        <Trigger
          trigger="click"
          placement="bottom"
          @update:visible="(v) => handleVisibleChange(v, 'click')"
        >
          <t-button>点击触发</t-button>
          <template #content>
            <div>这是一个点击触发的提示内容</div>
          </template>
        </Trigger>

        <Trigger
          trigger="focus"
          placement="right"
        >
          <t-input placeholder="聚焦触发" />
          <template #content>
            <div>这是一个聚焦触发的提示内容</div>
          </template>
        </Trigger>
      </div>
    </div>

    <div class="demo-section">
      <h2>不同位置</h2>
      <div class="demo-grid">
        <div class="demo-grid-row">
          <Trigger placement="top-start">
            <t-button>上左</t-button>
            <template #content>上左位置的提示</template>
          </Trigger>
          <Trigger placement="top">
            <t-button>上中</t-button>
            <template #content>上中位置的提示</template>
          </Trigger>
          <Trigger placement="top-end">
            <t-button>上右</t-button>
            <template #content>上右位置的提示</template>
          </Trigger>
        </div>

        <div class="demo-grid-row">
          <Trigger placement="left-start">
            <t-button>左上</t-button>
            <template #content>左上位置的提示</template>
          </Trigger>
          <div class="demo-center">
            <span>Trigger 组件</span>
          </div>
          <Trigger placement="right-start">
            <t-button>右上</t-button>
            <template #content>右上位置的提示</template>
          </Trigger>
        </div>

        <div class="demo-grid-row">
          <Trigger placement="left">
            <t-button>左中</t-button>
            <template #content>左中位置的提示</template>
          </Trigger>
          <div></div>
          <Trigger placement="right">
            <t-button>右中</t-button>
            <template #content>右中位置的提示</template>
          </Trigger>
        </div>

        <div class="demo-grid-row">
          <Trigger placement="left-end">
            <t-button>左下</t-button>
            <template #content>左下位置的提示</template>
          </Trigger>
          <div></div>
          <Trigger placement="right-end">
            <t-button>右下</t-button>
            <template #content>右下位置的提示</template>
          </Trigger>
        </div>

        <div class="demo-grid-row">
          <Trigger placement="bottom-start">
            <t-button>下左</t-button>
            <template #content>下左位置的提示</template>
          </Trigger>
          <Trigger placement="bottom">
            <t-button>下中</t-button>
            <template #content>下中位置的提示</template>
          </Trigger>
          <Trigger placement="bottom-end">
            <t-button>下右</t-button>
            <template #content>下右位置的提示</template>
          </Trigger>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>多种触发方式</h2>
      <div class="demo-row">
        <Trigger :trigger="['hover', 'click']">
          <t-button>悬停或点击</t-button>
          <template #content>
            <div>支持悬停或点击触发</div>
          </template>
        </Trigger>

        <Trigger trigger="contextmenu">
          <t-button>右键菜单</t-button>
          <template #content>
            <div>右键点击触发的菜单</div>
          </template>
        </Trigger>
      </div>
    </div>

    <div class="demo-section">
      <h2>延迟和自定义样式</h2>
      <div class="demo-row">
        <Trigger
          :show-delay="500"
          :hide-delay="200"
        >
          <t-button>延迟触发</t-button>
          <template #content>
            <div>延迟 500ms 显示，200ms 隐藏</div>
          </template>
        </Trigger>

        <Trigger
          :show-arrow="false"
          popup-class="custom-popup"
          :popup-style="{ background: 'linear-gradient(45deg, #667eea, #764ba2)', color: 'white', border: 'none' }"
        >
          <t-button>自定义样式</t-button>
          <template #content>
            <div>自定义样式的弹出层</div>
          </template>
        </Trigger>
      </div>
    </div>

    <div class="demo-section">
      <h2>受控模式</h2>
      <div class="demo-row">
        <div class="demo-controls">
          <t-button @click="visible1 = !visible1">
            {{ visible1 ? '隐藏' : '显示' }} Tooltip
          </t-button>
          <Trigger
            v-model:visible="visible1"
            trigger="click"
          >
            <t-button>受控的 Trigger</t-button>
            <template #content>
              <div>这是受控模式的 Trigger</div>
            </template>
          </Trigger>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>禁用状态</h2>
      <div class="demo-row">
        <Trigger disabled>
          <t-button disabled>禁用的触发器</t-button>
          <template #content>
            <div>这个不会显示</div>
          </template>
        </Trigger>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;

  h2 {
    margin-bottom: 16px;
    color: var(--u-text-color-primary, #333);
    border-bottom: 2px solid var(--u-color-neutral-3, #e0e0e0);
    padding-bottom: 8px;
  }
}

.demo-row {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.demo-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px;
  background: var(--u-bg-color-2, #fafbfb);
  border-radius: var(--u-radius-medium, 8px);
  border: 1px solid var(--u-color-neutral-3, #e0e0e0);
}

.demo-grid-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.demo-center {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: var(--u-bg-color-3, #fff);
  border: 2px dashed var(--u-color-neutral-4, #ccc);
  border-radius: var(--u-radius-default, 6px);
  color: var(--u-text-color-secondary, #666);
  font-weight: 500;
}

.demo-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

:global(.custom-popup) {
  border-radius: 12px !important;
  padding: 12px 16px !important;
  font-weight: 500 !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
}
</style>
