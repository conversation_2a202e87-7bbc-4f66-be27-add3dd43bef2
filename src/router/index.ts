import { createRouter, createWebHistory } from 'vue-router'
import type { MenuRouterItem } from '@xiaou66/u-web-ui'


export const settingRouters: MenuRouterItem[] = [
  {
    name: 'setting-group',
    path: 'setting-group',
    component: () => import('@/views/SettingDemo.vue'),
    meta: {
      title: 'setting-group',
      icon: 'i-p-left',
      menu: true
    }
  }
]
export const routes: MenuRouterItem[] = [
  {
    name: 'setting 组件',
    path: '/setting',
    component: () => import('@/views/SettingDemo.vue'),
    meta: {
      title: 'setting 组件',
      icon: 'i-p-left',
      menu: true
    },
    children: settingRouters
  },
  {
    name: 'PageHeader 组件',
    path: '/page-header',
    component: () => import('@/views/PageHeaderDemo.vue'),
    meta: {
      title: 'PageHeader 组件',
      icon: 'i-p-left',
      menu: true
    }
  },
  {
    name: 'SplitPanel 组件',
    path: '/split-panel',
    component: () => import('@/views/SplitPanelDemo.vue'),
    meta: {
      title: 'SplitPanel 组件',
      icon: 'i-p-left',
      menu: true
    }
  },
  {
    path: '/context-menu',
    name: 'context-menu',
    component: () => import('../views/ContextMenuDemo.vue'),
    meta: {
      title: '右键菜单',
      icon: 'i-p-left',
      menu: true
    }
  },
  {
    path: '/switch-plus',
    name: 'switch-plus',
    component: () => import('../views/SwitchPlusDemo.vue'),
    meta: {
      title: 'SwitchPlus 组件',
      icon: 'i-p-left',
      menu: true
    }
  },
  {
    path: '/back-top',
    name: 'back-top',
    component: () => import('../views/BackTopDemo.vue'),
    meta: {
      title: 'BackTop 回到顶部',
      icon: 'i-p-left',
      menu: true
    }
  },
  {
    path: '/result',
    name: 'result',
    component: () => import('../views/ResultDemo.vue'),
    meta: {
      title: 'Result 结果页',
      icon: 'i-p-left',
      menu: true
    }
  },
  {
    path: '/scrollbar',
    name: 'scrollbar',
    component: () => import('../views/ScrollbarDemo.vue'),
    meta: {
      title: 'Scrollbar 滚动条',
      icon: 'i-p-left',
      menu: true
    }
  },
  {
    path: '/lazy-loader',
    name: 'lazy-loader',
    component: () => import('../views/LazyLoaderDemo.vue'),
    meta: {
      title: 'LazyLoader 懒加载',
      icon: 'i-p-left',
      menu: true
    }
  }
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: routes
})

export default router
